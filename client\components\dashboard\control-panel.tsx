'use client';

import { useState } from 'react';
import {
  Calendar,
  FileDown,
  FileText,
  QrCode,
  Search,
  LayoutGrid,
  List,
  Filter,
  FileSpreadsheet,
  History,
  BarChart,
} from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { ProductVariants, TestReport } from '@/lib/types';
import { API_CONFIG, formatDateForApi, apiRequest } from '@/lib/api-config';

interface ControlPanelProps {
  selectedVariant: string;
  setSelectedVariant: (variant: string) => void;
  serialNumber: string;
  setSerialNumber: (serialNumber: string) => void;
  productVariants: ProductVariants;
  availableReports: TestReport[];
  onDownloadReport: () => void;
  viewMode?: 'card' | 'list';
  setViewMode?: (mode: 'card' | 'list') => void;
  historyMode?: boolean;
  setHistoryMode?: (mode: boolean) => void;
  onHistorySerialNumberSelect?: (serialNumber: string, date?: Date) => void;
  onHistoryDateSelect?: (date: Date | undefined) => void;
}

export function ControlPanel({
  selectedVariant,
  setSelectedVariant,
  serialNumber,
  setSerialNumber,
  productVariants,
  availableReports,
  onDownloadReport,
  viewMode,
  setViewMode,
  historyMode = false,
  setHistoryMode,
  onHistorySerialNumberSelect,
  onHistoryDateSelect,
}: ControlPanelProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [calendarOpen, setCalendarOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  const [availableReportFiles, setAvailableReportFiles] = useState<string[]>([]);
  const [isLoadingReports, setIsLoadingReports] = useState(false);

  // History view state
  const [historyDate, setHistoryDate] = useState<Date | undefined>(new Date());
  const [historyDateOpen, setHistoryDateOpen] = useState(false);
  const [availableSerialNumbers, setAvailableSerialNumbers] = useState<string[]>([]);
  const [selectedHistorySerialNumber, setSelectedHistorySerialNumber] = useState<string>('');
  const [isLoadingSerialNumbers, setIsLoadingSerialNumbers] = useState(false);

  // Fetch reports for selected date
  const fetchReportsForDate = async (date: Date) => {
    setIsLoadingReports(true);
    try {
      const formattedDate = formatDateForApi(date);
      const url = API_CONFIG.ENDPOINTS.REPORTS.BY_DATE(formattedDate);
      const result = await apiRequest(url);

      // Handle 404 (No reports found) as a valid response
      if (!result.success && result.error?.includes('404')) {
        console.log(`No reports found for date ${formattedDate}`);
        setAvailableReportFiles([]);
        setSelectedReport(null);
        return;
      }

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch reports');
      }

      const data = result.data;
      console.log('Reports data:', data);
      // Handle both possible response formats (files or filename array)
      const files = data.files || data.filename || [];
      setAvailableReportFiles(Array.isArray(files) ? files : [files]);
      setSelectedReport(null); // Reset selected report when date changes
    } catch (error) {
      console.error('Error fetching reports:', error);
      setAvailableReportFiles([]);
      setSelectedReport(null);
    } finally {
      setIsLoadingReports(false);
    }
  };

  // Fetch serial numbers for a specific date
  const fetchSerialNumbersForDate = async (date: Date) => {
    setIsLoadingSerialNumbers(true);
    try {
      const formattedDate = formatDateForApi(date);
      const url = API_CONFIG.ENDPOINTS.REPORTS.SERIAL_NUMBERS_BY_DATE(formattedDate);
      const result = await apiRequest(url);

      // Handle 404 (No reports found) as a valid response
      if (!result.success && result.error?.includes('404')) {
        console.log(`No reports found for date ${formattedDate}`);
        setAvailableSerialNumbers([]);
        setSelectedHistorySerialNumber('');
        return;
      }

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch serial numbers');
      }

      const data = result.data;
      console.log('Serial numbers data:', data);

      if (data.serialNumbers && Array.isArray(data.serialNumbers)) {
        setAvailableSerialNumbers(data.serialNumbers);
        // Select the first serial number if available
        if (data.serialNumbers.length > 0) {
          setSelectedHistorySerialNumber(data.serialNumbers[0]);
          // Call the callback if provided
          if (onHistorySerialNumberSelect) {
            onHistorySerialNumberSelect(data.serialNumbers[0], date);
          }
        } else {
          setSelectedHistorySerialNumber('');
        }
      } else {
        setAvailableSerialNumbers([]);
        setSelectedHistorySerialNumber('');
      }
    } catch (error) {
      console.error('Error fetching serial numbers:', error);
      setAvailableSerialNumbers([]);
      setSelectedHistorySerialNumber('');
    } finally {
      setIsLoadingSerialNumbers(false);
    }
  };

  // Handle history date selection
  const handleHistoryDateSelect = (date: Date | undefined) => {
    setHistoryDate(date);
    if (date) {
      // Call the callback if provided
      if (onHistoryDateSelect) {
        onHistoryDateSelect(date);
      }

      // Always fetch serial numbers for the selected date
      fetchSerialNumbersForDate(date);

      // Auto-close the popover after selecting a date
      setHistoryDateOpen(false);
    }
  };

  // Handle history serial number selection
  const handleHistorySerialNumberSelect = (serialNumber: string) => {
    setSelectedHistorySerialNumber(serialNumber);
    // Call the callback if provided
    if (onHistorySerialNumberSelect) {
      onHistorySerialNumberSelect(serialNumber, historyDate);
    }
  };

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    if (date) {
      // Log the selected date for debugging
      console.log('Selected date:', date);
      console.log('Date components:', {
        year: date.getFullYear(),
        month: date.getMonth() + 1, // 0-based, so add 1
        day: date.getDate(),
        hours: date.getHours(),
        minutes: date.getMinutes(),
        timezone: date.getTimezoneOffset() / -60, // Convert to hours and invert sign
      });

      fetchReportsForDate(date);

      // Auto-close the popover after selecting a date
      setCalendarOpen(false);
    }
  };

  // Handle report download
  const handleDownloadReport = () => {
    // If in history mode, use the history date for download
    if (historyMode && historyDate) {
      const formattedDate = formatDateForApi(historyDate);

      // Download the entire report for the selected date
      const downloadUrl = API_CONFIG.ENDPOINTS.REPORTS.DOWNLOAD_BY_DATE(formattedDate);

      // Create a hidden anchor element for direct download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${formattedDate}_12V3A2C.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      return;
    }

    // Regular download from dialog selection
    if (selectedDate && selectedReport) {
      const formattedDate = formatDateForApi(selectedDate);
      const downloadUrl = API_CONFIG.ENDPOINTS.REPORTS.DOWNLOAD_BY_DATE(
        formattedDate,
        selectedReport
      );

      // Create a hidden anchor element for direct download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${selectedReport}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Close the dialog
      setDialogOpen(false);
    }
  };

  return (
    <div className="mb-5">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {/* Product variant selection */}
        <Card className="border border-slate-200 shadow-md bg-white overflow-hidden">
          <CardContent className="p-3 flex items-center">
            <div className="flex-grow flex items-center">
              <div className="flex items-center bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg border border-slate-200 shadow-sm overflow-hidden w-full">
                <div className="flex items-center h-9 px-3 bg-gradient-to-r from-indigo-100/50 to-purple-100/50">
                  <Filter className="h-4 w-4 text-indigo-600 mr-1.5" />
                  <span className="text-sm font-medium text-indigo-700">Variant</span>
                </div>
                <div className="flex-grow">
                  <Select value={selectedVariant} onValueChange={setSelectedVariant}>
                    <SelectTrigger
                      id="variant-select"
                      className="border-0 shadow-none h-9 text-sm font-medium bg-transparent focus:ring-0 focus:ring-offset-0"
                    >
                      <SelectValue placeholder="Select product variant" />
                    </SelectTrigger>
                    <SelectContent className="bg-white border border-slate-200 shadow-lg">
                      {Object.keys(productVariants).map(variant => (
                        <SelectItem
                          key={variant}
                          value={variant}
                          className="hover:bg-indigo-50 focus:bg-indigo-50 cursor-pointer text-sm font-medium"
                        >
                          {variant}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Serial number input or History controls */}
        <Card className="border border-slate-200 shadow-md bg-white overflow-hidden">
          <CardContent className="p-3 flex items-center">
            <div className="flex-grow flex items-center">
              {historyMode ? (
                // History mode: Date and serial number controls
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 w-full">
                  {/* Date selector */}
                  <div className="flex items-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-slate-200 shadow-sm overflow-hidden h-9">
                    <Popover open={historyDateOpen} onOpenChange={setHistoryDateOpen}>
                      <PopoverTrigger asChild>
                        <button
                          type="button"
                          className="flex items-center h-full px-3 w-full justify-between"
                          title="Select Date"
                          aria-label="Select Date"
                        >
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 text-blue-600 mr-2" />
                            <span className="text-sm font-medium text-blue-700">
                              {historyDate ? format(historyDate, 'MMM d, yyyy') : 'Select date'}
                            </span>
                          </div>
                        </button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 border border-slate-200 shadow-lg">
                        <CalendarComponent
                          mode="single"
                          selected={historyDate}
                          onSelect={handleHistoryDateSelect}
                          initialFocus
                          className="rounded-md border-slate-200"
                          fromDate={new Date(2020, 0, 1)}
                          toDate={new Date(2030, 11, 31)}
                          disabled={date => date > new Date()}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  {/* Serial number dropdown */}
                  <div className="flex items-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-slate-200 shadow-sm overflow-hidden h-9">
                    <div className="flex-grow">
                      <Select
                        value={selectedHistorySerialNumber}
                        onValueChange={handleHistorySerialNumberSelect}
                        disabled={isLoadingSerialNumbers || availableSerialNumbers.length === 0}
                      >
                        <SelectTrigger
                          id="serial-select"
                          className="border-0 shadow-none h-full text-sm font-medium bg-transparent focus:ring-0 focus:ring-offset-0 pl-3"
                        >
                          <div className="flex items-center">
                            <QrCode className="h-4 w-4 text-blue-600 mr-2 shrink-0" />
                            <SelectValue
                              placeholder={
                                isLoadingSerialNumbers
                                  ? 'Loading...'
                                  : availableSerialNumbers.length === 0
                                  ? 'No serials available'
                                  : 'Select serial number'
                              }
                            />
                          </div>
                          {isLoadingSerialNumbers && (
                            <div className="ml-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                          )}
                        </SelectTrigger>
                        <SelectContent className="bg-white border border-slate-200 shadow-lg">
                          {availableSerialNumbers.map(sn => (
                            <SelectItem
                              key={sn}
                              value={sn}
                              className="hover:bg-blue-50 focus:bg-blue-50 cursor-pointer text-sm font-medium"
                            >
                              {sn}
                            </SelectItem>
                          ))}
                          {!isLoadingSerialNumbers && availableSerialNumbers.length === 0 && (
                            <SelectItem value="none" disabled>
                              No serial numbers available for{' '}
                              {historyDate ? format(historyDate, 'PPP') : 'selected date'}
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              ) : (
                // Live mode: Serial number input with QR code
                <div className="flex items-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-slate-200 shadow-sm overflow-hidden w-full h-9">
                  <div className="flex-grow relative flex items-center">
                    <QrCode className="h-4 w-4 text-blue-600 ml-3 mr-2" />
                    <Input
                      id="serial-input"
                      placeholder="Scan or enter serial number"
                      value={serialNumber}
                      onChange={e => setSerialNumber(e.target.value)}
                      className="border-0 shadow-none h-full text-sm font-medium bg-transparent focus:ring-0 focus:ring-offset-0 pl-0"
                    />
                  </div>
                  <div className="h-5 w-px bg-slate-200 mx-1"></div>
                  <button
                    type="button"
                    className="flex items-center h-full px-3 hover:bg-blue-100 transition-colors"
                    title="Search Serial"
                    aria-label="Search Serial"
                  >
                    <Search className="h-4 w-4 text-blue-600" />
                  </button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Reports Section */}
        <Card className="border border-slate-200 shadow-md bg-white overflow-hidden">
          <CardContent className="p-3 flex items-center">
            <div className="flex-grow flex items-center">
              {/* Download Report Dialog - Only accessible through the buttons */}
              <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                <DialogContent className="sm:max-w-[450px] bg-white border-slate-200 shadow-xl">
                  <DialogHeader>
                    <DialogTitle className="text-lg font-semibold text-slate-800">
                      Download Test Report
                    </DialogTitle>
                    <DialogDescription className="text-slate-600 text-sm">
                      Select a date and report to download test results.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-3 py-3">
                    {/* Date selection */}
                    <div className="space-y-1.5">
                      <label className="text-xs font-medium text-slate-700 flex items-center gap-1.5">
                        <Calendar className="h-3.5 w-3.5 text-emerald-500" />
                        Select Date
                      </label>
                      <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-medium bg-white border-slate-200 shadow-sm h-9 text-sm"
                          >
                            <Calendar className="mr-1.5 h-3.5 w-3.5 text-emerald-500" />
                            {selectedDate ? format(selectedDate, 'PPP') : 'Pick a date'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 border border-slate-200 shadow-lg">
                          <CalendarComponent
                            mode="single"
                            selected={selectedDate}
                            onSelect={handleDateSelect}
                            initialFocus
                            className="rounded-md border-slate-200"
                            // Ensure dates are treated as local dates (midnight in local timezone)
                            fromDate={new Date(2020, 0, 1)} // Min date: Jan 1, 2020
                            toDate={new Date(2030, 11, 31)} // Max date: Dec 31, 2030
                            disabled={date => {
                              // Disable future dates (beyond today)
                              return date > new Date();
                            }}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    {/* Report selection */}
                    <div className="space-y-1.5">
                      <label
                        htmlFor="report-select"
                        className="text-xs font-medium text-slate-700 flex items-center gap-1.5"
                      >
                        <FileText className="h-3.5 w-3.5 text-emerald-500" />
                        Available Reports
                      </label>
                      <Select
                        value={selectedReport || ''}
                        onValueChange={setSelectedReport}
                        disabled={isLoadingReports || availableReportFiles.length === 0}
                      >
                        <SelectTrigger
                          id="report-select"
                          className="w-full bg-white border-slate-200 shadow-sm h-9 text-sm font-medium"
                        >
                          <SelectValue
                            placeholder={isLoadingReports ? 'Loading reports...' : 'Select report'}
                          />
                          {isLoadingReports && (
                            <div className="ml-2 h-4 w-4 animate-spin rounded-full border-2 border-emerald-500 border-t-transparent"></div>
                          )}
                        </SelectTrigger>
                        <SelectContent className="bg-white border border-slate-200 shadow-lg">
                          {availableReportFiles.map(reportFile => (
                            <SelectItem
                              key={reportFile}
                              value={reportFile}
                              className="hover:bg-emerald-50 focus:bg-emerald-50 cursor-pointer text-sm font-medium"
                            >
                              {reportFile}
                            </SelectItem>
                          ))}
                          {!isLoadingReports && availableReportFiles.length === 0 && (
                            <SelectItem value="none" disabled>
                              No reports available for{' '}
                              {selectedDate ? format(selectedDate, 'PPP') : 'selected date'}
                            </SelectItem>
                          )}
                          {isLoadingReports && (
                            <SelectItem value="loading" disabled>
                              <div className="flex items-center gap-2">
                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-emerald-500 border-t-transparent"></div>
                                <span>Loading reports...</span>
                              </div>
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="flex justify-end gap-2">
                    <DialogClose asChild>
                      <Button
                        variant="outline"
                        className="bg-white border-slate-200 hover:bg-slate-50 h-9 text-sm font-medium"
                      >
                        Cancel
                      </Button>
                    </DialogClose>
                    <Button
                      onClick={handleDownloadReport}
                      disabled={!selectedReport}
                      className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 shadow-md h-9 text-sm font-medium"
                    >
                      <FileDown className="mr-1.5 h-4 w-4" />
                      Download
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>

              {/* Action buttons */}
              <div className="flex items-center gap-2 mt-2 w-full">
                {/* View toggle button */}
                {setHistoryMode && (
                  <button
                    type="button"
                    onClick={() => {
                      if (setHistoryMode) {
                        // If switching to history mode, initialize the date and fetch serial numbers
                        if (!historyMode && historyDate) {
                          fetchSerialNumbersForDate(historyDate);
                        }
                        setHistoryMode(!historyMode);

                        // Always use card view for live data and list view for history
                        if (setViewMode) {
                          setViewMode('card');
                        }
                      }
                    }}
                    className={`flex items-center justify-center h-9 px-3 rounded-lg border border-slate-200 shadow-sm transition-all duration-200 ${
                      historyMode
                        ? 'bg-gradient-to-r from-indigo-500 to-blue-500 hover:from-indigo-600 hover:to-blue-600 text-white'
                        : 'bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 text-blue-700'
                    }`}
                    title={historyMode ? 'Switch to Live View' : 'Switch to History View'}
                    aria-label={historyMode ? 'Switch to Live View' : 'Switch to History View'}
                  >
                    {historyMode ? (
                      <LayoutGrid
                        className={`h-4 w-4 ${historyMode ? 'text-white' : 'text-blue-600'}`}
                      />
                    ) : (
                      <History
                        className={`h-4 w-4 ${historyMode ? 'text-white' : 'text-blue-600'}`}
                      />
                    )}
                    <span className="text-xs font-medium ml-1.5">
                      {historyMode ? 'Live' : 'History'}
                    </span>
                  </button>
                )}

                {/* Download report button - modified to fill remaining space */}
                <button
                  type="button"
                  onClick={() =>
                    historyMode && historyDate ? handleDownloadReport() : setDialogOpen(true)
                  }
                  className={`flex items-center justify-center h-9 px-3 rounded-lg border border-slate-200 shadow-sm transition-all duration-200 flex-1 ${
                    historyMode && !historyDate
                      ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
                      : 'bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white'
                  }`}
                  title={
                    historyMode
                      ? `Download Report for ${
                          historyDate ? format(historyDate, 'MMM d, yyyy') : 'selected date'
                        }`
                      : 'Select Report to Download'
                  }
                  aria-label="Download Report"
                  disabled={historyMode && !historyDate}
                >
                  <FileDown
                    className={`h-4 w-4 mr-1.5 ${
                      historyMode && !historyDate ? 'text-slate-400' : 'text-white'
                    }`}
                  />
                  <span className="text-xs font-medium">
                    {historyMode && !historyDate
                      ? 'Select Date'
                      : historyMode && historyDate
                      ? `Download ${format(historyDate, 'MMM d')} Report`
                      : 'Download Report'}
                  </span>
                </button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
