'use client';

import { <PERSON>, <PERSON>pu, Zap, Battery, BatteryCharging, RefreshCw, XCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { DeviceData, TestResults } from '@/lib/types';
import { TEST_STATUS, STATUS } from '@/lib/constants';
import {
  formatTime,
  getCardBorderColor,
  hasDeviceFailed,
  getBatteryIndicator,
} from '@/lib/dashboard-utils';
import { CheckCircle, AlertCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface DeviceCardProps {
  device: DeviceData;
  onSelect: (deviceId: string) => void;
  compact?: boolean;
  testResults?: TestResults;
  isHistory?: boolean;
}

// Helper function to render status icon
function getStatusIcon(status: boolean | string) {
  // Handle boolean values (true = OK, false = ERROR)
  if (typeof status === 'boolean') {
    return status ? (
      <CheckCircle className="h-5 w-5 text-green-600" />
    ) : (
      <AlertCircle className="h-5 w-5 text-red-600" />
    );
  }

  // Handle string values like 'ok', 'error', 'warning'
  if (status === 'ok') return <CheckCircle className="h-5 w-5 text-green-600" />;
  if (status === 'error') return <AlertCircle className="h-5 w-5 text-red-600" />;
  if (status === 'warning') return <AlertCircle className="h-5 w-5 text-amber-500" />;

  // Default for unknown status
  return <AlertCircle className="h-5 w-5 text-gray-400" />;
}

export function DeviceCard({
  device,
  onSelect,
  compact = false,
  testResults,
  isHistory = false,
}: DeviceCardProps) {
  // Debug log to see device status
  console.log(`Device ${device.id} status:`, {
    testStatus: device.testStatus,
    testStatusEnum: TEST_STATUS.IN_PROGRESS,
    isEqual: device.testStatus === TEST_STATUS.IN_PROGRESS,
    isCharging: device.isCharging,
    isDischarging: device.isDischarging,
    isChargingAndDischarging: device.isChargingAndDischarging,
    progress: device.progress,
    hasPCB: device.testStatus !== TEST_STATUS.NO_PCB,
    chargingVoltage: device.chargingVoltage,
    chargingCurrent: device.chargingCurrent,
    dischargingVoltage: device.dischargingVoltage,
    dischargingCurrent: device.dischargingCurrent,
    timeRemaining: device.timeRemaining,
  });
  // Check if the device has failed any tests
  const deviceHasFailed = (): boolean => {
    // For history devices, use a simplified check based on status values
    if (isHistory) {
      // Check if any of the required status values are false
      if (typeof device.chargingStatus === 'boolean' && device.chargingStatus === false) {
        return true;
      }
      if (typeof device.dischargingStatus === 'boolean' && device.dischargingStatus === false) {
        return true;
      }
      if (
        typeof device.chargingAndDischargingStatus === 'boolean' &&
        device.chargingAndDischargingStatus === false &&
        device.hasState3Occurred
      ) {
        return true;
      }

      // If all required status values are true, the device has passed
      return false;
    }

    // For live devices, use the original logic
    // Only consider the device as failed if it's in a completed state
    if (device.testStatus !== TEST_STATUS.COMPLETED && device.testStatus !== TEST_STATUS.FAILED) {
      return false;
    }

    // First check if any of the status values are false (boolean false = failure)
    if (typeof device.chargingStatus === 'boolean' && device.chargingStatus === false) {
      return true;
    }
    if (typeof device.dischargingStatus === 'boolean' && device.dischargingStatus === false) {
      return true;
    }
    if (
      typeof device.chargingAndDischargingStatus === 'boolean' &&
      device.chargingAndDischargingStatus === false &&
      device.hasState3Occurred
    ) {
      return true;
    }

    // Check if any status is 'error' (string value)
    if (
      device.chargingStatus === 'error' ||
      device.dischargingStatus === 'error' ||
      (device.hasState3Occurred && device.chargingAndDischargingStatus === 'error')
    ) {
      return true;
    }

    // Then check the test results
    return device.testStatus === TEST_STATUS.FAILED || hasDeviceFailed(device, testResults);
  };

  return (
    <TooltipProvider delayDuration={100}>
      <Card
        className={`${
          device.testStatus === TEST_STATUS.COMPLETED ||
          device.testStatus === TEST_STATUS.FAILED ||
          deviceHasFailed()
            ? device.testStatus === TEST_STATUS.FAILED || deviceHasFailed()
              ? 'border border-red-200 bg-red-50 shadow-md shadow-red-100/50'
              : 'border border-green-200 bg-green-50 shadow-md shadow-green-100/50'
            : `${getCardBorderColor(
                device.isCharging,
                device.isDischarging,
                device.isChargingAndDischarging,
                device.batteryLevel
              )} border border-slate-200 shadow-sm`
        } cursor-pointer hover:shadow-xl hover:-translate-y-1 transition-all duration-300 ease-out transform hover:scale-[1.02] ${
          device.testStatus === TEST_STATUS.COMPLETED ||
          device.testStatus === TEST_STATUS.FAILED ||
          deviceHasFailed()
            ? device.testStatus === TEST_STATUS.FAILED || deviceHasFailed()
              ? 'bg-gradient-to-br from-red-50 to-red-100'
              : 'bg-gradient-to-br from-green-50 to-green-100'
            : 'bg-gradient-to-br from-white to-slate-50'
        } h-full rounded-xl overflow-hidden relative card-state-transition`}
        onClick={() => onSelect(device.name)}
      >
        <CardContent className={`flex flex-col ${compact ? 'p-3' : 'p-4'} h-full`}>
          {/* Top row: PCB icon with number, status icons for charging and discharging */}
          <div className={`flex items-center justify-between w-full ${compact ? 'mb-2' : 'mb-3'}`}>
            <div className="flex items-center gap-1">
              <div className="flex items-center gap-1">
                <div className="bg-slate-100 p-1.5 rounded-full">
                  <Cpu className={compact ? 'h-4 w-4 text-slate-700' : 'h-5 w-5 text-slate-700'} />
                </div>
                <span className={compact ? 'text-base font-bold' : 'text-lg font-bold'}>
                  {device.name}
                </span>
              </div>
              <div className="flex items-center gap-1">
                {/* Charging status icon */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      className={`flex items-center gap-1 transition-all duration-300 ease-out ${
                        device.testStatus === TEST_STATUS.NO_PCB
                          ? 'bg-gray-50'
                          : typeof device.chargingStatus === 'boolean'
                          ? device.chargingStatus
                            ? 'bg-green-50'
                            : 'bg-red-50'
                          : device.chargingStatus === 'error'
                          ? 'bg-red-50'
                          : 'bg-green-50'
                      } ${compact ? 'px-1.5 py-0.5' : 'px-2 py-1'} rounded-md hover:scale-105`}
                    >
                      <BatteryCharging
                        className={`h-3.5 w-3.5 ${
                          device.testStatus === TEST_STATUS.NO_PCB
                            ? 'text-gray-400'
                            : device.chargingStatus === STATUS.OK
                            ? 'text-green-600'
                            : device.chargingStatus === STATUS.ERROR
                            ? 'text-red-600'
                            : 'text-gray-400'
                        }`}
                      />
                      {getStatusIcon(device.chargingStatus)}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="top" align="center" sideOffset={10} avoidCollisions={true}>
                    {device.chargingStatusMessage}
                  </TooltipContent>
                </Tooltip>

                {/* Discharging status icon */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      className={`flex items-center gap-1 transition-all duration-300 ease-out ${
                        device.testStatus === TEST_STATUS.NO_PCB
                          ? 'bg-gray-50'
                          : typeof device.dischargingStatus === 'boolean'
                          ? device.dischargingStatus
                            ? 'bg-orange-50'
                            : 'bg-red-50'
                          : device.dischargingStatus === 'error'
                          ? 'bg-red-50'
                          : 'bg-orange-50'
                      } ${compact ? 'px-1.5 py-0.5' : 'px-2 py-1'} rounded-md hover:scale-105`}
                    >
                      <Battery
                        className={`h-3.5 w-3.5 ${
                          device.testStatus === TEST_STATUS.NO_PCB
                            ? 'text-gray-400'
                            : device.dischargingStatus === STATUS.OK
                            ? 'text-orange-600'
                            : device.dischargingStatus === STATUS.ERROR
                            ? 'text-red-600'
                            : 'text-gray-400'
                        }`}
                      />
                      {getStatusIcon(device.dischargingStatus)}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="top" align="center" sideOffset={10} avoidCollisions={true}>
                    {device.dischargingStatusMessage}
                  </TooltipContent>
                </Tooltip>

                {/* Charging and Discharging combined status icon (Sequence 3) */}
                {(device.isChargingAndDischarging || device.hasState3Occurred) && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className={`flex items-center gap-1 transition-all duration-300 ease-out ${
                          device.testStatus === TEST_STATUS.NO_PCB
                            ? 'bg-gray-50'
                            : typeof device.chargingAndDischargingStatus === 'boolean'
                            ? device.chargingAndDischargingStatus
                              ? 'bg-teal-50'
                              : 'bg-red-50'
                            : device.chargingAndDischargingStatus === 'error'
                            ? 'bg-red-50'
                            : 'bg-teal-50'
                        } ${compact ? 'px-1.5 py-0.5' : 'px-2 py-1'} rounded-md hover:scale-105`}
                      >
                        <RefreshCw
                          className={`h-3.5 w-3.5 ${
                            device.testStatus === TEST_STATUS.NO_PCB
                              ? 'text-gray-400'
                              : device.chargingAndDischargingStatus === STATUS.OK
                              ? 'text-teal-600'
                              : device.chargingAndDischargingStatus === STATUS.ERROR
                              ? 'text-red-600'
                              : 'text-gray-400'
                          }`}
                        />
                        {getStatusIcon(device.chargingAndDischargingStatus)}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      side="top"
                      align="center"
                      sideOffset={10}
                      avoidCollisions={true}
                    >
                      {device.chargingAndDischargingStatusMessage}
                    </TooltipContent>
                  </Tooltip>
                )}
              </div>
            </div>
            <div className="flex items-center gap-1">
              <div className="flex items-center gap-1 bg-slate-100 px-2 py-1 rounded-full">
                <Clock className="h-3.5 w-3.5 text-muted-foreground" />
                <span className="text-xs">{formatTime(device.timeRemaining)}</span>
              </div>
              <div
                className={`${
                  device.testStatus === TEST_STATUS.NO_PCB
                    ? 'bg-gray-100'
                    : device.isChargingAndDischarging
                    ? 'bg-teal-50'
                    : device.isCharging
                    ? 'bg-green-50'
                    : device.isDischarging
                    ? 'bg-orange-50'
                    : device.batteryLevel < 20
                    ? 'bg-red-50'
                    : 'bg-slate-100'
                } px-1.5 py-1 rounded-full`}
              >
                {getBatteryIndicator(
                  device.testStatus === TEST_STATUS.NO_PCB ? 0 : device.batteryLevel,
                  device.isCharging,
                  device.isChargingAndDischarging,
                  'card',
                  device.testStatus !== TEST_STATUS.NO_PCB
                )}
              </div>
            </div>
          </div>

          {/* Battery level and status */}
          <div className={compact ? 'mb-2' : 'mb-3'}>
            {device.testStatus === TEST_STATUS.NO_PCB ? (
              <div className={`bg-red-50 rounded-lg ${compact ? 'p-2' : 'p-3'} text-center`}>
                <p className="text-red-600 font-medium text-sm">No PCB</p>
              </div>
            ) : device.testStatus === TEST_STATUS.NOT_STARTED ? (
              <div className={`bg-gray-100 rounded-lg ${compact ? 'p-2' : 'p-3'} text-center`}>
                <p className="text-gray-600 font-medium text-sm">Test Not Started</p>
              </div>
            ) : device.testStatus === TEST_STATUS.COMPLETED ||
              device.testStatus === TEST_STATUS.FAILED ||
              deviceHasFailed() ? (
              <div
                className={`${
                  device.testStatus === TEST_STATUS.FAILED || deviceHasFailed()
                    ? 'bg-red-50'
                    : 'bg-green-50'
                } rounded-lg ${compact ? 'p-2' : 'p-3'} text-center relative`}
              >
                {device.testStatus === TEST_STATUS.FAILED || deviceHasFailed() ? (
                  <div className="flex items-center justify-center gap-1">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <p className="text-red-600 font-medium text-sm">Test Failed</p>
                  </div>
                ) : (
                  <p className="text-green-600 font-medium text-sm">Test Completed</p>
                )}
              </div>
            ) : (
              <>
                <div className="flex justify-between w-full mb-1">
                  <span className={compact ? 'text-base font-bold' : 'text-lg font-bold'}>
                    {device.progress || 0}%
                  </span>
                  <span
                    className={`${compact ? 'text-xs' : 'text-sm'} font-medium ${
                      device.isChargingAndDischarging
                        ? 'text-teal-600'
                        : device.isCharging
                        ? 'text-green-600'
                        : device.isDischarging
                        ? 'text-orange-600'
                        : 'text-gray-600'
                    }`}
                  >
                    {device.isChargingAndDischarging
                      ? 'Charging and Discharging'
                      : device.isCharging
                      ? 'Charging'
                      : device.isDischarging
                      ? 'Discharging'
                      : ''}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 mb-1.5 overflow-hidden">
                  <div
                    className={`h-2 rounded-full transition-all duration-500 ease-out progress-animate ${
                      // Determine the progress bar color based on the current state and status
                      device.isChargingAndDischarging
                        ? // Combined charging and discharging state
                          device.chargingAndDischargingStatus === STATUS.OK
                          ? 'bg-teal-500'
                          : device.chargingAndDischargingStatus === STATUS.ERROR
                          ? 'bg-red-500'
                          : 'bg-teal-500'
                        : device.isCharging
                        ? // Charging state
                          device.chargingStatus === STATUS.OK
                          ? 'bg-green-500'
                          : device.chargingStatus === STATUS.ERROR
                          ? 'bg-red-500'
                          : 'bg-green-500'
                        : device.isDischarging
                        ? // Discharging state
                          device.dischargingStatus === STATUS.OK
                          ? 'bg-orange-500'
                          : device.dischargingStatus === STATUS.ERROR
                          ? 'bg-red-500'
                          : 'bg-orange-500'
                        : // Default state
                          'bg-gray-500'
                    }`}
                    style={{ width: `${Math.min(device.progress || 0, 100)}%` }}
                  ></div>
                </div>
              </>
            )}
          </div>

          {/* Electrical metrics section - showing charging, discharging, or combined */}
          <div className={`grid grid-cols-2 ${compact ? 'gap-2' : 'gap-4'}`}>
            {/* Charging metrics */}
            <div
              className={`transition-all duration-300 ease-out hover:scale-[1.02] ${
                device.testStatus === TEST_STATUS.NO_PCB
                  ? 'opacity-60 bg-white/50 border-slate-200'
                  : device.isCharging
                  ? 'opacity-100 bg-gradient-to-br from-green-50 to-green-100 border-green-200'
                  : device.isChargingAndDischarging
                  ? 'opacity-100 bg-gradient-to-br from-teal-50 to-teal-100 border-teal-200'
                  : device.testStatus === TEST_STATUS.FAILED
                  ? 'opacity-100 bg-gradient-to-br from-red-50 to-red-100 border-red-200'
                  : 'opacity-80 bg-white/50 border-slate-200'
              } border rounded-lg ${compact ? 'p-1.5' : 'p-2'}`}
            >
              <div className="flex items-center gap-1 mb-0.5">
                <Zap
                  className={`${compact ? 'h-3 w-3' : 'h-4 w-4'} ${
                    device.testStatus === TEST_STATUS.NO_PCB
                      ? 'text-gray-400'
                      : device.isCharging
                      ? 'text-green-600'
                      : device.isChargingAndDischarging
                      ? 'text-teal-500'
                      : device.testStatus === TEST_STATUS.FAILED
                      ? 'text-red-600'
                      : 'text-muted-foreground'
                  }`}
                />
                <span className="text-xs font-medium">Charging</span>
              </div>
              <div className="grid grid-cols-1 gap-0.5">
                <div className="flex justify-between">
                  <span className="text-xs text-muted-foreground">V:</span>
                  <span className="text-xs font-medium">
                    {(device.chargingVoltage || 0).toFixed(2)}V
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-muted-foreground">A:</span>
                  <span className="text-xs font-medium">
                    {(device.chargingCurrent || 0).toFixed(3)}A
                  </span>
                </div>
              </div>
            </div>

            {/* Discharging metrics */}
            <div
              className={`transition-all duration-300 ease-out hover:scale-[1.02] ${
                device.testStatus === TEST_STATUS.NO_PCB
                  ? 'opacity-60 bg-white/50 border-slate-200'
                  : device.isDischarging
                  ? 'opacity-100 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200'
                  : device.isChargingAndDischarging
                  ? 'opacity-100 bg-gradient-to-br from-teal-50 to-teal-100 border-teal-200'
                  : device.testStatus === TEST_STATUS.FAILED
                  ? 'opacity-100 bg-gradient-to-br from-red-50 to-red-100 border-red-200'
                  : 'opacity-80 bg-white/50 border-slate-200'
              } border rounded-lg ${compact ? 'p-1.5' : 'p-2'}`}
            >
              <div className="flex items-center gap-1 mb-0.5">
                <Battery
                  className={`${compact ? 'h-3 w-3' : 'h-4 w-4'} ${
                    device.testStatus === TEST_STATUS.NO_PCB
                      ? 'text-gray-400'
                      : device.isDischarging
                      ? 'text-orange-600'
                      : device.isChargingAndDischarging
                      ? 'text-teal-500'
                      : device.testStatus === TEST_STATUS.FAILED
                      ? 'text-red-600'
                      : 'text-muted-foreground'
                  }`}
                />
                <span className="text-xs font-medium">Discharging</span>
              </div>
              <div className="grid grid-cols-1 gap-0.5">
                <div className="flex justify-between">
                  <span className="text-xs text-muted-foreground">V:</span>
                  <span className="text-xs font-medium">
                    {(device.dischargingVoltage || 0).toFixed(2)}V
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-muted-foreground">A:</span>
                  <span className="text-xs font-medium">
                    {(device.dischargingCurrent || 0).toFixed(3)}A
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
