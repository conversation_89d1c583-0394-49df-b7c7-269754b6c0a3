/* Custom animations for device cards and list views */

/* Fade in up animation */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade in animation */
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Scale in animation */
@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Slide in from left */
@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide in from right */
@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for loading states */
@keyframes pulse-soft {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Bounce in animation */
@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.4s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.4s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

.animate-pulse-soft {
  animation: pulse-soft 2s ease-in-out infinite;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

/* Staggered animation delays */
.animate-delay-75 {
  animation-delay: 75ms;
}

.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-150 {
  animation-delay: 150ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

/* Transition utilities */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.transition-elastic {
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Loading shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Status change animations */
.status-change {
  animation: bounce-in 0.4s ease-out;
}

/* Progress bar animations */
.progress-animate {
  transition: width 0.5s ease-out;
}

/* Card state transitions */
.card-state-transition {
  transition: all 0.3s ease-out;
}

.card-state-transition:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* List row transitions */
.list-row-transition {
  transition: all 0.2s ease-out;
}

.list-row-transition:hover {
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* View mode transition */
.view-transition {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.view-transition-enter {
  opacity: 0;
  transform: scale(0.95);
}

.view-transition-enter-active {
  opacity: 1;
  transform: scale(1);
}

.view-transition-exit {
  opacity: 1;
  transform: scale(1);
}

.view-transition-exit-active {
  opacity: 0;
  transform: scale(0.95);
}

/* Loading state animations */
.loading-fade-in {
  animation: fade-in 0.3s ease-out;
}

.loading-pulse {
  animation: pulse-soft 1.5s ease-in-out infinite;
}

/* Smooth data transitions */
.data-transition {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.data-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.data-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
}

/* Icon transitions */
.icon-transition {
  transition: all 0.2s ease-out;
}

.icon-transition:hover {
  transform: scale(1.1);
}

/* Status change animation */
.status-indicator {
  transition: all 0.3s ease-out;
}

.status-indicator.success {
  animation: bounce-in 0.5s ease-out;
}

.status-indicator.error {
  animation: bounce-in 0.5s ease-out;
}

/* Table row animations */
.table-row-enter {
  opacity: 0;
  transform: translateX(-10px);
}

.table-row-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease-out;
}

/* Card grid animations */
.card-grid-enter {
  opacity: 0;
  transform: scale(0.9);
}

.card-grid-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: all 0.4s ease-out;
}
