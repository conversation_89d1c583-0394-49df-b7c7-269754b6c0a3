/* Custom animations for device cards and list views */

/* Fade in up animation */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade in animation */
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Scale in animation */
@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Slide in from left */
@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide in from right */
@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for loading states */
@keyframes pulse-soft {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Bounce in animation */
@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.4s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.4s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

.animate-pulse-soft {
  animation: pulse-soft 2s ease-in-out infinite;
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out;
}

/* Staggered animation delays */
.animate-delay-75 {
  animation-delay: 75ms;
}

.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-150 {
  animation-delay: 150ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

/* Transition utilities */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.transition-elastic {
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Loading shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Status change animations */
.status-change {
  animation: bounce-in 0.4s ease-out;
}

/* Progress bar animations */
.progress-animate {
  transition: width 0.5s ease-out;
}

/* Card state transitions */
.card-state-transition {
  transition: all 0.3s ease-out;
}

.card-state-transition:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* List row transitions */
.list-row-transition {
  transition: all 0.2s ease-out;
}

.list-row-transition:hover {
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* View mode transition */
.view-transition {
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.view-transition-enter {
  opacity: 0;
  transform: scale(0.95);
}

.view-transition-enter-active {
  opacity: 1;
  transform: scale(1);
}

.view-transition-exit {
  opacity: 1;
  transform: scale(1);
}

.view-transition-exit-active {
  opacity: 0;
  transform: scale(0.95);
}

/* Loading state animations */
.loading-fade-in {
  animation: fade-in 0.3s ease-out;
}

.loading-pulse {
  animation: pulse-soft 1.5s ease-in-out infinite;
}

/* Smooth data transitions */
.data-transition {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.data-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.data-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
}

/* Icon transitions */
.icon-transition {
  transition: all 0.2s ease-out;
}

.icon-transition:hover {
  transform: scale(1.1);
}

/* Status change animation */
.status-indicator {
  transition: all 0.3s ease-out;
}

.status-indicator.success {
  animation: bounce-in 0.5s ease-out;
}

.status-indicator.error {
  animation: bounce-in 0.5s ease-out;
}

/* Table row animations */
.table-row-enter {
  opacity: 0;
  transform: translateX(-10px);
}

.table-row-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease-out;
}

/* Card grid animations */
.card-grid-enter {
  opacity: 0;
  transform: scale(0.9);
}

.card-grid-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: all 0.4s ease-out;
}

/* PCB Panel Specific Animations */

/* Page load animations */
@keyframes page-slide-in {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dashboard-header-slide-down {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes control-panel-slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pcb-grid-fade-in {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* PCB Testing State Animations */
@keyframes pcb-test-start {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  }
}

@keyframes pcb-test-complete {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.05);
  }
  50% {
    transform: scale(0.98);
  }
  75% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pcb-charging-pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);
  }
}

@keyframes pcb-discharging-pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(249, 115, 22, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(249, 115, 22, 0);
  }
}

@keyframes pcb-error-shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

/* Page transition animations */
.page-enter {
  animation: page-slide-in 0.6s ease-out;
}

.dashboard-header-enter {
  animation: dashboard-header-slide-down 0.5s ease-out;
}

.control-panel-enter {
  animation: control-panel-slide-up 0.6s ease-out 0.2s both;
}

.pcb-grid-enter {
  animation: pcb-grid-fade-in 0.7s ease-out 0.4s both;
}

/* PCB State Classes */
.pcb-test-starting {
  animation: pcb-test-start 0.8s ease-out;
}

.pcb-test-completed {
  animation: pcb-test-complete 0.6s ease-out;
}

.pcb-charging {
  animation: pcb-charging-pulse 2s ease-in-out infinite;
}

.pcb-discharging {
  animation: pcb-discharging-pulse 2s ease-in-out infinite;
}

.pcb-error {
  animation: pcb-error-shake 0.5s ease-in-out;
}

/* PCB Panel Loading States */
.pcb-loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.pcb-data-loading {
  opacity: 0.6;
  animation: pulse-soft 1.5s ease-in-out infinite;
}

/* PCB Status Transitions */
.pcb-status-success {
  animation: bounce-in 0.5s ease-out;
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
}

.pcb-status-error {
  animation: pcb-error-shake 0.5s ease-out;
  background: linear-gradient(135deg, #fef2f2, #fecaca);
}

.pcb-status-warning {
  animation: bounce-in 0.5s ease-out;
  background: linear-gradient(135deg, #fffbeb, #fed7aa);
}

/* PCB Progress Animations */
.pcb-progress-bar {
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.pcb-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

/* PCB Card Hover Effects */
.pcb-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pcb-card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* PCB List Row Effects */
.pcb-list-row {
  transition: all 0.2s ease-out;
}

.pcb-list-row:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.05), transparent);
}

/* PCB Panel Page Transitions */
.pcb-panel-transition-enter {
  animation: page-slide-in 0.8s ease-out;
}

.pcb-panel-transition-exit {
  animation: page-slide-in 0.4s ease-in reverse;
}

/* PCB Dashboard Specific Animations */
.pcb-dashboard-enter {
  animation: pcb-grid-fade-in 0.6s ease-out;
}

.pcb-metric-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pcb-metric-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* PCB Connection Status Animations */
.pcb-connected {
  animation: pcb-test-start 1s ease-out;
}

.pcb-disconnected {
  animation: pcb-error-shake 0.5s ease-out;
}

/* PCB Data Loading States */
.pcb-data-enter {
  animation: fade-in-up 0.4s ease-out;
}

.pcb-data-update {
  animation: bounce-in 0.3s ease-out;
}

/* PCB Battery Level Animations */
.pcb-battery-low {
  animation: pcb-error-shake 2s ease-in-out infinite;
}

.pcb-battery-charging {
  animation: pcb-charging-pulse 2s ease-in-out infinite;
}

.pcb-battery-full {
  animation: pcb-status-success 0.5s ease-out;
}

/* PCB Test Sequence Animations */
.pcb-sequence-1 {
  animation: pcb-charging-pulse 2s ease-in-out infinite;
}

.pcb-sequence-2 {
  animation: pcb-discharging-pulse 2s ease-in-out infinite;
}

.pcb-sequence-3 {
  animation: pcb-charging-pulse 1s ease-in-out infinite,
    pcb-discharging-pulse 1s ease-in-out infinite 0.5s;
}

/* PCB Control Panel Animations */
.pcb-control-button {
  transition: all 0.2s ease-out;
}

.pcb-control-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pcb-control-button:active {
  transform: scale(0.98);
}

/* PCB History View Animations */
.pcb-history-enter {
  animation: slide-in-left 0.5s ease-out;
}

.pcb-live-enter {
  animation: slide-in-right 0.5s ease-out;
}

/* PCB Loading Skeleton for Data */
.pcb-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

/* PCB Success/Error State Transitions */
.pcb-test-success {
  animation: pcb-test-complete 0.8s ease-out, pcb-status-success 0.5s ease-out 0.8s;
}

.pcb-test-failure {
  animation: pcb-error-shake 0.5s ease-out, pcb-status-error 0.5s ease-out 0.5s;
}
